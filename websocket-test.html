<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; }
        #log { height: 300px; overflow-y: scroll; background: #f8f9fa; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket 连接测试工具</h1>
        
        <div class="section">
            <h3>1. 配置参数</h3>
            <label>API URL:</label>
            <input type="text" id="apiUrl" value="https://api.tiptop.cn" />
            
            <label>Authorization Token:</label>
            <input type="text" id="token" value="Bearer fMMQLoQ9IRIsjf3kVWLnWGk7gDV4IEnpkbdsoQOaLRqve" />
            
            <label>Client Type:</label>
            <select id="clientType">
                <option value="python_tool">python_tool (推荐)</option>
                <option value="web_browser">web_browser (测试用)</option>
                <option value="mobile_app">mobile_app</option>
            </select>
            
            <label>Business Type:</label>
            <select id="businessType">
                <option value="text">text</option>
                <option value="story">story</option>
                <option value="prompt">prompt</option>
                <option value="storyboard">storyboard</option>
            </select>
        </div>

        <div class="section">
            <h3>2. WebSocket 认证</h3>
            <button onclick="authenticateWebSocket()">建立 WebSocket 认证</button>
            <button onclick="connectWebSocket()">连接 WebSocket</button>
            <button onclick="disconnectWebSocket()">断开连接</button>
            <div id="connectionStatus" class="info">状态: 未连接</div>
        </div>

        <div class="section">
            <h3>3. 测试文本生成</h3>
            <label>提示词:</label>
            <textarea id="prompt" rows="3">{"提示词": "测试积分不足场景：请写一首关于春天的诗"}</textarea>
            
            <label>Max Tokens:</label>
            <input type="number" id="maxTokens" value="5000" />
            
            <button onclick="testTextGeneration()">测试文本生成</button>
        </div>

        <div class="section">
            <h3>4. 日志输出</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log"></div>
        </div>
    </div>

    <script>
        let websocket = null;
        let sessionId = null;
        let websocketUrl = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateConnectionStatus(status, className = 'info') {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.textContent = `状态: ${status}`;
            statusDiv.className = className;
        }

        async function authenticateWebSocket() {
            try {
                const apiUrl = document.getElementById('apiUrl').value;
                const token = document.getElementById('token').value;
                const clientType = document.getElementById('clientType').value;
                const businessType = document.getElementById('businessType').value;

                log(`开始 WebSocket 认证...`);
                log(`Client Type: ${clientType}, Business Type: ${businessType}`);

                const response = await fetch(`${apiUrl}/py-api/websocket/auth`, {
                    method: 'POST',
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        client_type: clientType,
                        business_type: businessType
                    })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    sessionId = result.data.session_id;
                    websocketUrl = result.data.websocket_url;
                    log(`认证成功! Session ID: ${sessionId}`, 'success');
                    log(`WebSocket URL: ${websocketUrl}`, 'success');
                    updateConnectionStatus('认证成功，可以连接', 'success');
                } else {
                    log(`认证失败: ${result.message} (Code: ${result.code})`, 'error');
                    updateConnectionStatus('认证失败', 'error');
                }
            } catch (error) {
                log(`认证异常: ${error.message}`, 'error');
                updateConnectionStatus('认证异常', 'error');
            }
        }

        function connectWebSocket() {
            if (!sessionId || !websocketUrl) {
                log('请先完成 WebSocket 认证', 'error');
                return;
            }

            try {
                websocket = new WebSocket(`${websocketUrl}?session_id=${sessionId}`);
                
                websocket.onopen = function(event) {
                    log('WebSocket 连接已建立', 'success');
                    updateConnectionStatus('已连接', 'success');
                };

                websocket.onmessage = function(event) {
                    log(`收到消息: ${event.data}`, 'info');
                };

                websocket.onclose = function(event) {
                    log(`WebSocket 连接已关闭: ${event.code} - ${event.reason}`, 'info');
                    updateConnectionStatus('已断开', 'info');
                };

                websocket.onerror = function(error) {
                    log(`WebSocket 错误: ${error}`, 'error');
                    updateConnectionStatus('连接错误', 'error');
                };

            } catch (error) {
                log(`连接异常: ${error.message}`, 'error');
                updateConnectionStatus('连接异常', 'error');
            }
        }

        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
                log('主动断开 WebSocket 连接', 'info');
                updateConnectionStatus('已断开', 'info');
            }
        }

        async function testTextGeneration() {
            try {
                const apiUrl = document.getElementById('apiUrl').value;
                const token = document.getElementById('token').value;
                const prompt = document.getElementById('prompt').value;
                const maxTokens = document.getElementById('maxTokens').value;

                log(`开始测试文本生成...`);

                const response = await fetch(`${apiUrl}/py-api/ai/text/generate-with-websocket`, {
                    method: 'POST',
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        prompt: prompt,
                        max_tokens: maxTokens,
                        context: 'test_points_insufficient',
                        websocket_session_id: sessionId || ''
                    })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    log(`文本生成任务创建成功! Task ID: ${result.data.task_id}`, 'success');
                    log(`预估成本: ${result.data.estimated_cost}`, 'info');
                } else {
                    log(`文本生成失败: ${result.message} (Code: ${result.code})`, 'error');
                    if (result.code === 1006) {
                        log(`积分不足测试成功！`, 'success');
                    }
                }
            } catch (error) {
                log(`文本生成异常: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
